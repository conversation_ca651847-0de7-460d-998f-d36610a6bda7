use dioxus::prelude::*;
use std::path::PathBuf;
use std::str::FromStr;

mod config;
mod edit_mod;
mod edit_modpack;
mod installations;
mod modpacks;
mod mods;
mod utils;

fn main() {
    dioxus::launch(App);
}

#[derive(Clone, PartialEq)]
enum AppState {
    MainMenu,
    Modpacks,
    ModpackEdit(String),
    ModpackAction(String, String), // modpack_name, action
    Mods,
    ModEdit(String),
    Installations,
    Config,
}

#[component]
fn App() -> Element {
    let app_state = use_signal(|| AppState::MainMenu);
    let config = use_signal(|| config::init());
    let status_message = use_signal(|| String::new());
    let modpack_list = use_signal(|| utils::get_modpack_names(config()));
    let mod_list = use_signal(|| utils::get_mod_names(config()));

    rsx! {
        div {
            style: "font-family: monospace; padding: 20px; background-color: #1a1a1a; color: #00ff00; min-height: 100vh;",

            h1 {
                style: "color: #ff6600; text-align: center; border: 2px solid #ff6600; padding: 10px; margin-bottom: 20px;",
                "🌪️ BREEZE GUI - MINECRAFT MOD MANAGER 🌪️"
            }

            if !status_message().is_empty() {
                div {
                    style: "background-color: #333; border: 1px solid #666; padding: 10px; margin-bottom: 20px; color: #ffff00;",
                    "STATUS: {status_message()}"
                    button {
                        style: "margin-left: 10px; background-color: #666; color: white; border: none; padding: 5px;",
                        onclick: move |_| status_message.set(String::new()),
                        "CLEAR"
                    }
                }
            }

            match app_state() {
                AppState::MainMenu => rsx! {
                    MainMenu {
                        app_state: app_state,
                        config: config(),
                    }
                },
                AppState::Modpacks => rsx! {
                    ModpacksView {
                        app_state: app_state,
                        config: config(),
                        modpack_list: modpack_list(),
                        status_message: status_message,
                    }
                },
                AppState::ModpackEdit(modpack_name) => rsx! {
                    ModpackEditView {
                        app_state: app_state,
                        config: config(),
                        modpack_name: modpack_name,
                        status_message: status_message,
                    }
                },
                AppState::Mods => rsx! {
                    ModsView {
                        app_state: app_state,
                        config: config(),
                        mod_list: mod_list(),
                        status_message: status_message,
                    }
                },
                AppState::ModEdit(mod_name) => rsx! {
                    ModEditView {
                        app_state: app_state,
                        config: config(),
                        mod_name: mod_name,
                        status_message: status_message,
                    }
                },
                AppState::Installations => rsx! {
                    InstallationsView {
                        app_state: app_state,
                        status_message: status_message,
                    }
                },
                AppState::Config => rsx! {
                    ConfigView {
                        app_state: app_state,
                        config: config,
                        status_message: status_message,
                    }
                },
                _ => rsx! { div { "Unknown state" } }
            }
        }
    }
}

#[component]
fn MainMenu(app_state: Signal<AppState>, config: config::Config) -> Element {
    rsx! {
        div {
            style: "text-align: center;",

            h2 {
                style: "color: #00ffff; border-bottom: 2px solid #00ffff; padding-bottom: 10px;",
                "MAIN MENU - SELECT YOUR DESTINY"
            }

            div {
                style: "display: flex; flex-direction: column; align-items: center; gap: 15px; margin-top: 30px;",

                button {
                    style: "width: 300px; height: 60px; font-size: 18px; background-color: #ff0066; color: white; border: 3px solid #ff0066; cursor: pointer; font-weight: bold;",
                    onclick: move |_| app_state.set(AppState::Modpacks),
                    "📦 MODPACKS"
                }

                button {
                    style: "width: 300px; height: 60px; font-size: 18px; background-color: #0066ff; color: white; border: 3px solid #0066ff; cursor: pointer; font-weight: bold;",
                    onclick: move |_| app_state.set(AppState::Mods),
                    "🔧 MODS"
                }

                button {
                    style: "width: 300px; height: 60px; font-size: 18px; background-color: #66ff00; color: black; border: 3px solid #66ff00; cursor: pointer; font-weight: bold;",
                    onclick: move |_| app_state.set(AppState::Installations),
                    "⚙️ INSTALLATIONS"
                }

                button {
                    style: "width: 300px; height: 60px; font-size: 18px; background-color: #ffff00; color: black; border: 3px solid #ffff00; cursor: pointer; font-weight: bold;",
                    onclick: move |_| app_state.set(AppState::Config),
                    "⚙️ CONFIG"
                }
            }

            div {
                style: "margin-top: 40px; padding: 20px; border: 2px dashed #666; background-color: #222;",
                h3 { style: "color: #ff6600;", "CURRENT CONFIG:" }
                p { style: "color: #ccc;", "Minecraft Directory: {config.dot_minecraft}" }
            }
        }
    }
}

#[component]
fn ModpacksView(
    app_state: Signal<AppState>,
    config: config::Config,
    modpack_list: Vec<String>,
    status_message: Signal<String>,
) -> Element {
    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #ff0066; border-bottom: 2px solid #ff0066; padding-bottom: 10px;",
                "📦 MODPACK MANAGEMENT"
            }

            div {
                style: "display: flex; gap: 20px; margin: 20px 0;",

                button {
                    style: "padding: 15px 25px; background-color: #ff6600; color: white; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        move |_| {
                            let success = modpacks::stash(config.clone(), false);
                            if success {
                                status_message.set("Mods successfully stashed into modpack!".to_string());
                            } else {
                                status_message.set("Failed to stash mods - no mods found or error occurred".to_string());
                            }
                        }
                    },
                    "📥 STASH CURRENT MODS"
                }

                button {
                    style: "padding: 15px 25px; background-color: #0066ff; color: white; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: move |_| {
                        // Import functionality would need async handling - simplified for now
                        status_message.set("Import functionality - use file dialog (not implemented in simple GUI)".to_string());
                    },
                    "📁 IMPORT MODPACK"
                }

                button {
                    style: "padding: 15px 25px; background-color: #66ff00; color: black; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        move |_| {
                            let minecraft_path = PathBuf::from_str(&config.dot_minecraft).expect("Invalid minecraft path");
                            let _ = open::that(minecraft_path.join("modpacks"));
                            status_message.set("Opening modpacks directory!".to_string());
                        }
                    },
                    "📂 OPEN FOLDER"
                }
            }

            h3 {
                style: "color: #00ffff; margin-top: 30px;",
                "AVAILABLE MODPACKS ({modpack_list.len()})"
            }

            if modpack_list.is_empty() {
                div {
                    style: "padding: 20px; border: 2px dashed #666; text-align: center; color: #999;",
                    "NO MODPACKS FOUND - CREATE ONE BY STASHING YOUR CURRENT MODS!"
                }
            } else {
                div {
                    style: "display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; margin-top: 20px;",
                    for modpack_name in modpack_list {
                        div {
                            key: "{modpack_name}",
                            style: "border: 2px solid #ff0066; padding: 15px; background-color: #2a2a2a; cursor: pointer;",
                            onclick: move |_| app_state.set(AppState::ModpackEdit(modpack_name.clone())),

                            h4 { style: "color: #ff0066; margin: 0 0 10px 0;", "{modpack_name}" }
                            p { style: "color: #ccc; margin: 0;", "Click to manage this modpack" }
                        }
                    }
                }
            }
        }
    }
}

#[component]
fn BackButton(app_state: Signal<AppState>) -> Element {
    rsx! {
        button {
            style: "margin-bottom: 20px; padding: 10px 20px; background-color: #666; color: white; border: none; cursor: pointer; font-weight: bold;",
            onclick: move |_| app_state.set(AppState::MainMenu),
            "⬅️ BACK TO MAIN MENU"
        }
    }
}

#[component]
fn ModpackEditView(
    app_state: Signal<AppState>,
    config: config::Config,
    modpack_name: String,
    status_message: Signal<String>,
) -> Element {
    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #ff0066; border-bottom: 2px solid #ff0066; padding-bottom: 10px;",
                "📦 EDITING MODPACK: {modpack_name}"
            }

            div {
                style: "display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap;",

                button {
                    style: "padding: 15px 25px; background-color: #00ff00; color: black; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        let modpack_name = modpack_name.clone();
                        move |_| {
                            let _minecraft_path = PathBuf::from_str(&config.dot_minecraft).expect("Invalid minecraft path");
                            let has_mods = utils::has_mods(config.clone());

                            if has_mods {
                                status_message.set("You have mods installed! Please clear or stash them first before loading a modpack.".to_string());
                            } else {
                                // Simplified load - in real implementation this would extract the zip
                                status_message.set(format!("Loading modpack '{}' - this would extract mods from the zip file", modpack_name));
                            }
                        }
                    },
                    "🚀 LOAD MODPACK"
                }

                button {
                    style: "padding: 15px 25px; background-color: #ffff00; color: black; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: move |_| {
                        status_message.set("Rename functionality - would show input dialog (simplified in GUI)".to_string());
                    },
                    "✏️ RENAME"
                }

                button {
                    style: "padding: 15px 25px; background-color: #ff0000; color: white; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        let modpack_name = modpack_name.clone();
                        move |_| {
                            // Delete modpack
                            let minecraft_path = PathBuf::from_str(&config.dot_minecraft).expect("Invalid minecraft path");
                            let modpack_file = minecraft_path.join("modpacks").join(format!("{}.zip", modpack_name));

                            match std::fs::remove_file(&modpack_file) {
                                Ok(_) => {
                                    status_message.set(format!("Modpack '{}' deleted successfully!", modpack_name));
                                    app_state.set(AppState::Modpacks);
                                },
                                Err(e) => {
                                    status_message.set(format!("Failed to delete modpack: {}", e));
                                }
                            }
                        }
                    },
                    "🗑️ DELETE"
                }
            }

            div {
                style: "margin-top: 30px; padding: 20px; border: 2px solid #ff0066; background-color: #2a2a2a;",
                h3 { style: "color: #ff0066;", "MODPACK INFO" }
                p { style: "color: #ccc;", "Name: {modpack_name}" }
                p { style: "color: #ccc;", "Location: {config.dot_minecraft}/modpacks/{modpack_name}.zip" }
                p { style: "color: #999; font-style: italic;", "Load: Extracts mods from this modpack to your mods folder" }
                p { style: "color: #999; font-style: italic;", "Rename: Changes the modpack name" }
                p { style: "color: #999; font-style: italic;", "Delete: Permanently removes this modpack file" }
            }
        }
    }
}

#[component]
fn ModsView(
    app_state: Signal<AppState>,
    config: config::Config,
    mod_list: Vec<String>,
    status_message: Signal<String>,
) -> Element {
    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #0066ff; border-bottom: 2px solid #0066ff; padding-bottom: 10px;",
                "🔧 MOD MANAGEMENT"
            }

            div {
                style: "display: flex; gap: 20px; margin: 20px 0;",

                button {
                    style: "padding: 15px 25px; background-color: #ff0000; color: white; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        move |_| {
                            let success = mods::clear(config.clone(), false);
                            if success {
                                status_message.set("All mods cleared successfully!".to_string());
                            } else {
                                status_message.set("Failed to clear mods - no mods found".to_string());
                            }
                        }
                    },
                    "🗑️ CLEAR ALL MODS"
                }

                button {
                    style: "padding: 15px 25px; background-color: #66ff00; color: black; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        move |_| {
                            let minecraft_path = PathBuf::from_str(&config.dot_minecraft).expect("Invalid minecraft path");
                            let _ = open::that(minecraft_path.join("mods"));
                            status_message.set("Opening mods directory!".to_string());
                        }
                    },
                    "📂 OPEN MODS FOLDER"
                }
            }

            h3 {
                style: "color: #00ffff; margin-top: 30px;",
                "INSTALLED MODS ({mod_list.len()})"
            }

            if mod_list.is_empty() {
                div {
                    style: "padding: 20px; border: 2px dashed #666; text-align: center; color: #999;",
                    "NO MODS FOUND - Install some mods or load a modpack!"
                }
            } else {
                div {
                    style: "display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; margin-top: 20px;",
                    for mod_name in mod_list {
                        div {
                            key: "{mod_name}",
                            style: "border: 2px solid #0066ff; padding: 15px; background-color: #2a2a2a; cursor: pointer;",
                            onclick: move |_| app_state.set(AppState::ModEdit(mod_name.clone())),

                            h4 { style: "color: #0066ff; margin: 0 0 10px 0;", "{mod_name}" }
                            p { style: "color: #ccc; margin: 0;", "Click to manage this mod" }
                        }
                    }
                }
            }
        }
    }
}

#[component]
fn ModEditView(
    app_state: Signal<AppState>,
    config: config::Config,
    mod_name: String,
    status_message: Signal<String>,
) -> Element {
    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #0066ff; border-bottom: 2px solid #0066ff; padding-bottom: 10px;",
                "🔧 EDITING MOD: {mod_name}"
            }

            div {
                style: "display: flex; gap: 20px; margin: 20px 0;",

                button {
                    style: "padding: 15px 25px; background-color: #ff0000; color: white; border: none; font-size: 16px; cursor: pointer; font-weight: bold;",
                    onclick: {
                        let config = config.clone();
                        let mod_name = mod_name.clone();
                        move |_| {
                            // Delete mod
                            let minecraft_path = PathBuf::from_str(&config.dot_minecraft).expect("Invalid minecraft path");
                            let mod_file = minecraft_path.join("mods").join(format!("{}.jar", mod_name));

                            match std::fs::remove_file(&mod_file) {
                                Ok(_) => {
                                    status_message.set(format!("Mod '{}' deleted successfully!", mod_name));
                                    app_state.set(AppState::Mods);
                                },
                                Err(e) => {
                                    status_message.set(format!("Failed to delete mod: {}", e));
                                }
                            }
                        }
                    },
                    "🗑️ DELETE MOD"
                }
            }

            div {
                style: "margin-top: 30px; padding: 20px; border: 2px solid #0066ff; background-color: #2a2a2a;",
                h3 { style: "color: #0066ff;", "MOD INFO" }
                p { style: "color: #ccc;", "Name: {mod_name}" }
                p { style: "color: #ccc;", "Location: {config.dot_minecraft}/mods/{mod_name}.jar" }
                p { style: "color: #999; font-style: italic;", "Delete: Permanently removes this mod file" }
            }
        }
    }
}

#[component]
fn InstallationsView(app_state: Signal<AppState>, status_message: Signal<String>) -> Element {
    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #66ff00; border-bottom: 2px solid #66ff00; padding-bottom: 10px;",
                "⚙️ MOD LOADER INSTALLATIONS"
            }

            div {
                style: "display: flex; flex-direction: column; gap: 20px; margin: 20px 0; align-items: center;",

                button {
                    style: "width: 400px; height: 80px; font-size: 20px; background-color: #ff6600; color: white; border: none; cursor: pointer; font-weight: bold;",
                    onclick: move |_| {
                        status_message.set("Fabric installer would be downloaded and executed (simplified in GUI)".to_string());
                    },
                    "🧵 INSTALL FABRIC"
                }

                button {
                    style: "width: 400px; height: 80px; font-size: 20px; background-color: #9966ff; color: white; border: none; cursor: pointer; font-weight: bold;",
                    onclick: move |_| {
                        status_message.set("Quilt installer would be downloaded and executed (simplified in GUI)".to_string());
                    },
                    "🪡 INSTALL QUILT"
                }

                button {
                    style: "width: 400px; height: 80px; font-size: 20px; background-color: #ff3366; color: white; border: none; cursor: pointer; font-weight: bold;",
                    onclick: move |_| {
                        status_message.set("NeoForge installer would be downloaded and executed (simplified in GUI)".to_string());
                    },
                    "🔥 INSTALL NEOFORGE"
                }
            }

            div {
                style: "margin-top: 40px; padding: 20px; border: 2px solid #66ff00; background-color: #2a2a2a;",
                h3 { style: "color: #66ff00;", "MOD LOADER INFO" }
                p { style: "color: #ccc;", "Fabric: Lightweight modding framework" }
                p { style: "color: #ccc;", "Quilt: Fork of Fabric with additional features" }
                p { style: "color: #ccc;", "NeoForge: Modern continuation of Minecraft Forge" }
                p { style: "color: #999; font-style: italic;", "Note: These buttons would download and run the respective installers" }
            }
        }
    }
}

#[component]
fn ConfigView(
    app_state: Signal<AppState>,
    config: Signal<config::Config>,
    status_message: Signal<String>,
) -> Element {
    let mut minecraft_path = use_signal(|| config().dot_minecraft.clone());

    rsx! {
        div {
            BackButton { app_state: app_state }

            h2 {
                style: "color: #ffff00; border-bottom: 2px solid #ffff00; padding-bottom: 10px;",
                "⚙️ CONFIGURATION"
            }

            div {
                style: "margin: 20px 0;",

                h3 { style: "color: #ffff00;", "MINECRAFT DIRECTORY" }

                div {
                    style: "display: flex; gap: 10px; align-items: center; margin: 10px 0;",

                    input {
                        style: "flex: 1; padding: 10px; font-size: 16px; background-color: #333; color: white; border: 2px solid #666;",
                        value: "{minecraft_path()}",
                        oninput: move |evt| minecraft_path.set(evt.value()),
                    }

                    button {
                        style: "padding: 10px 20px; background-color: #ffff00; color: black; border: none; cursor: pointer; font-weight: bold;",
                        onclick: move |_| {
                            // Update config
                            let new_config = config::Config {
                                version: 1,
                                dot_minecraft: minecraft_path(),
                            };
                            config.set(new_config);
                            status_message.set("Configuration updated! (Note: This is simplified - real implementation would save to file)".to_string());
                        },
                        "💾 SAVE"
                    }
                }
            }

            div {
                style: "margin-top: 40px; padding: 20px; border: 2px solid #ffff00; background-color: #2a2a2a;",
                h3 { style: "color: #ffff00;", "CURRENT SETTINGS" }
                p { style: "color: #ccc;", "Minecraft Directory: {config().dot_minecraft}" }
                p { style: "color: #999; font-style: italic;", "This is where your mods and modpacks are stored" }
                p { style: "color: #999; font-style: italic;", "Make sure this path exists and is writable" }
            }
        }
    }
}
