use dialoguer::{theme::ColorfulTheme, Select};
use dioxus::prelude::*;
use dioxus_router::prelude::*;
mod config;
mod edit_mod;
mod edit_modpack;
mod installations;
mod modpacks;
mod mods;
mod utils;

// #[tokio::main]
// async fn main() {
fn main() {
    dioxus::launch(App);

    // let config = config::init();

    // let selections = &["Modpacks", "Mods", "Installations"]; // TODO: add a "Configs" and "Installations" option

    // loop {
    //     println!();

    //     let selection = Select::with_theme(&ColorfulTheme::default())
    //         .with_prompt("What would you like to manage")
    //         .default(0)
    //         .items(&selections[..])
    //         .interact_opt()
    //         .unwrap();

    //     if let Some(selection) = selection {
    //         match selection {
    //             0 => modpacks::cli(config.clone()),
    //             1 => mods::cli(config.clone()),
    //             2 => installations::cli().await,
    //             _ => panic!(),
    //         }
    //     } else {
    //         break;
    //     }
    // }
}

#[component]
fn App() -> Element {
    let name = "dave";

    rsx! {
        h1 { "Hello, {name}!", style {
            color: "blue"
         } }
        div { class: "my-class", id: "my-id",
            for i in 0..5 {
                div { "FizzBuzz: {i}" }
            }
        }
    }
}
