[package]
name = "breeze_gui"
version = "1.3.0"
edition = "2021"

[dependencies]
dialoguer = { version = "0.11.0", features = ["fuzzy-select"] }
libium = { git = "https://github.com/gorilla-devs/libium", version = "1.32.0" }
open = "5.3.1"
serde = "1.0.210"
serde_json = "1.0.128"
tokio = { version = "1", features = ["full"] }
reqwest = "0.12.12"
sevenz-rust = "0.6.1"
rfd = "0.15.2"
dioxus = { version = "0.6.3", features = ["desktop"] }

[target.'cfg(target_os = "linux")'.dependencies]
wayland-sys = { version = "0.31.6", features = ["dlopen"] }
